# 测试策略指南

作者：开发团队  
日期：2025-08-07  
版本：1.0  
关联文档：[架构文档](docs/architecture.md#63-测试架构策略)

---

## 概述

本指南基于 Story 1.13 的实践经验，为团队提供标准化的测试策略选择框架。目标是确保测试稳定性（≥95% 成功率）、执行效率（≤10秒/测试）和开发体验的一致性。

## 核心原则

1. **稳定性优先**：选择最稳定的测试策略，避免因时序问题导致的不稳定测试
2. **场景驱动**：根据具体测试场景选择合适的策略，而非一刀切
3. **可维护性**：优先选择简单、易理解的测试方法
4. **CI 友好**：确保本地和 CI 环境行为一致

## 测试策略选择标准

### 1. 长时间等待测试 (>5秒)

**推荐策略：真实时间 + DOM 条件等待**

```typescript
// ✅ 推荐：使用真实时间 + DOM 等待
describe('长加载测试', () => {
  beforeEach(() => {
    vi.useRealTimers(); // 使用真实时间
  });

  it('应该显示长加载提示', async () => {
    render(<Reader />);
    
    // 基于 DOM 状态等待，而非时间推进
    const longLoadingIndicator = await screen.findByTestId(
      'long-loading-indicator',
      { timeout: 15000 } // 给足够的超时时间
    );
    
    expect(longLoadingIndicator).toBeInTheDocument();
  });
});
```

**为什么选择这种策略：**
- 避免 fake timers 与 React 渲染周期的复杂交互
- 基于实际 DOM 状态变化，更接近真实用户体验
- 减少测试维护成本

### 2. 短时间精确控制 (<5秒)

**推荐策略：Fake Timers + 推进工具**

```typescript
// ✅ 推荐：短时间场景使用 fake timers
describe('短时间控制测试', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('应该在300ms后隐藏骨架屏', async () => {
    render(<Component />);
    
    // 精确控制时间推进
    await __advanceAndFlush__(300);
    
    expect(screen.queryByTestId('skeleton')).not.toBeInTheDocument();
  });
});
```

### 3. 复杂异步交互

**推荐策略：DOM 状态驱动**

```typescript
// ✅ 推荐：基于 DOM 状态变化
it('应该处理复杂的异步交互', async () => {
  render(<ComplexComponent />);
  
  // 触发异步操作
  fireEvent.click(screen.getByRole('button', { name: '开始处理' }));
  
  // 等待第一个状态变化
  await waitFor(() => {
    expect(screen.getByText('处理中...')).toBeInTheDocument();
  });
  
  // 等待最终状态
  await waitFor(() => {
    expect(screen.getByText('处理完成')).toBeInTheDocument();
  }, { timeout: 10000 });
});
```

### 4. API 调用测试

**推荐策略：Mock + 真实时间**

```typescript
// ✅ 推荐：Mock API + 真实异步行为
it('应该正确处理 API 调用', async () => {
  // Mock API 响应
  vi.mocked(apiClient.sendMessage).mockResolvedValue({
    id: '123',
    content: 'AI 回复',
    trace_id: 'test-trace'
  });
  
  render(<ChatComponent />);
  
  fireEvent.click(screen.getByRole('button', { name: '发送' }));
  
  // 等待 API 调用完成和 UI 更新
  await waitFor(() => {
    expect(screen.getByText('AI 回复')).toBeInTheDocument();
  });
  
  expect(apiClient.sendMessage).toHaveBeenCalledTimes(1);
});
```

## 异步推进机制详解

### __advanceAndFlush__ 工具

```typescript
// 全局推进工具的使用
declare global {
  var __advanceAndFlush__: (ms: number) => Promise<void>;
}

// 使用示例
await __advanceAndFlush__(1000); // 推进1秒并清空微任务队列
```

**何时使用：**
- 需要精确控制时间推进的场景
- 测试定时器相关功能
- 短时间内的状态变化验证

**注意事项：**
- 仅在 fake timers 环境下有效
- 会自动处理微任务队列清空
- 包含 React act 包装

### 微任务让步机制

```typescript
// 在关键异步回调中添加微任务让步
const handleAsyncCallback = async () => {
  // 执行异步操作
  await someAsyncOperation();
  
  // 微任务让步，确保 React 状态更新提交到 DOM
  schedule.microtask(() => {});
  
  // 继续后续操作
};
```

## 测试调试最佳实践

### 1. 调试信息输出

```typescript
// 在测试失败时输出有用的调试信息
it('调试友好的测试', async () => {
  render(<Component />);
  
  try {
    await waitFor(() => {
      expect(screen.getByTestId('target')).toBeInTheDocument();
    });
  } catch (error) {
    // 输出当前 DOM 状态
    console.log('当前 DOM:', screen.debug());
    
    // 输出所有可用的测试 ID
    const allTestIds = screen.getAllByTestId(/.*/).map(el => el.getAttribute('data-testid'));
    console.log('可用的 testId:', allTestIds);
    
    throw error;
  }
});
```

### 2. 测试状态快照

```typescript
// 创建测试状态快照工具
const captureTestSnapshot = () => ({
  dom: screen.debug(),
  timestamp: Date.now(),
  timers: vi.getTimerCount(),
  // 其他有用的状态信息
});
```

## 常见问题与解决方案

### Q: 测试在 CI 环境中失败，但本地通过？

**A:** 通常是时序问题，建议：
1. 增加超时时间
2. 使用真实时间策略
3. 基于 DOM 状态等待而非时间推进

### Q: 如何处理测试中的竞态条件？

**A:** 
1. 使用 `waitFor` 等待条件满足
2. 避免使用 `setTimeout` 等不确定的等待
3. 基于实际状态变化进行断言

### Q: 何时应该使用 fake timers？

**A:** 
- 测试时间相关功能（定时器、延迟等）
- 需要精确控制时间流逝的场景
- 短时间内的状态变化验证

## 性能考虑

### 测试执行时间优化

1. **并行执行**：使用 Vitest 的并行能力
2. **选择性运行**：使用 `test.only` 或文件过滤
3. **Mock 优化**：避免不必要的真实网络请求
4. **清理策略**：及时清理定时器和事件监听器

### CI 环境优化

```typescript
// 在 CI 环境中调整超时时间
const isCI = process.env.CI === 'true';
const timeout = isCI ? 30000 : 10000;

await waitFor(() => {
  // 断言逻辑
}, { timeout });
```

## 团队协作规范

### 代码审查检查清单

- [ ] 测试策略选择是否合理？
- [ ] 是否有足够的超时时间？
- [ ] 错误信息是否清晰？
- [ ] 是否遵循了异步等待最佳实践？
- [ ] 测试是否稳定可重复？

### 测试命名约定

```typescript
// ✅ 好的测试命名
describe('Reader 组件', () => {
  describe('长加载场景', () => {
    it('应该在10秒后显示长加载提示', () => {});
    it('应该在用户取消时停止加载', () => {});
  });
});

// ❌ 避免的命名
describe('测试', () => {
  it('测试1', () => {});
});
```

---

## 参考资料

- [Vitest 官方文档](https://vitest.dev/)
- [React Testing Library 最佳实践](https://testing-library.com/docs/react-testing-library/intro/)
- [Story 1.13 实施记录](docs/stories/1.13.reader-test-stability-fix-and-async-timing-optimization.md)
- [项目架构文档](docs/architecture.md)

---

*本指南将根据团队实践经验持续更新和完善。*
